<title>Steady Node</title><link href=style.css rel=stylesheet><script>window.onload=async()=>{for(r of document.querySelectorAll("input[type='ip']"))r.type="text",r.minLength=7,r.maxLength=15,r.size=15,r.pattern="((^|\\.)((25[0-5])|(2[0-4]\\d)|(1\\d\\d)|([1-9]?\\d))){4}$",r.title="IP Address",r.required=!0;var e=await fetch("/ipconfig"),t=await e.arrayBuffer(),n=new TextDecoder("iso-8859-1").decode(t).split("\n").reduce((function(e,t){return e[t.split(":")[0]]=t.split(":")[1],e}),{});for(const[e,t]of Object.entries(n)){var r=document.getElementsByName(e)[0];r&&("checkbox"==r.type?r.checked="1"==t?1:0:"radio"==r.type?(r=document.querySelector(`input[name='${e}'][value='${t}']`))&&(r.checked=1):r.value=t)}function syncUnifiedFields(){var e=document.getElementsByName("d_dev")[0],t=document.getElementsByName("s_ip")[0];e&&t&&(t.value=e.value);var n=document.getElementsByName("d_mask")[0],r=document.getElementsByName("s_mask")[0];n&&r&&(r.value=n.value);var a=document.querySelector('input[name="ipmode"]:checked'),o=document.getElementsByName("dhcp_en")[0];a&&o&&(o.checked="0"==a.value)}document.addEventListener("change",syncUnifiedFields),document.addEventListener("input",syncUnifiedFields),syncUnifiedFields()};</script><h1><i>Steady Node</i></h1><div><nav><a href=/ >Port Config</a> <a href=/device.html>Device Config</a></nav><form action=/ip-config method=post class=container><h3 style="grid-column:1/span 4">Device</h3><div><fieldset><legend>IP Mode</legend><label class=radio><input name=ipmode type=radio value=0> DHCP Server (Default)</label> <label class=radio><input name=ipmode type=radio value=1> Static IP</label></fieldset><fieldset><legend>IP Settings</legend><input name=dhcp_en type=checkbox style=display:none> <label>Steady Node IP<input name=d_dev type=ip value=********></label> <label>Net Mask<input name=d_mask type=ip value=*********></label> <label>iPad IP<input name=d_host type=ip value=********></label> <input name=s_gw type=ip style=display:none value=********> <label>Local DNS Name<input name=dns_name type=text value=steadynode.local maxlength=32></label> <input name=s_ip type=ip style=display:none value=********> <input name=s_mask type=ip style=display:none value=*********></fieldset></div><div><fieldset><legend>Firmware</legend><label>Version<input name=f_v readonly></label> <label>ArtNET-ID<input name=f_id readonly></label></fieldset><input type=submit value="Apply Settings"id=submit><div style=height:40px></div><input type=submit value="Reset Config"form=rstCfg> <input type=submit value="Reboot Device"form=rstDev> <input type=submit value="Reboot into DFU Mode"form=rstDfu></div></form></div><form action=/reset-config method=post hidden id=rstCfg></form><form action=/reset-device method=post hidden id=rstDev></form><form action=/reset-dfu method=post hidden id=rstDfu></form>