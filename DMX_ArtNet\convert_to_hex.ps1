# PowerShell script to convert device.html to hex format for fsdata.c
# This will help update the fsdata.c with the simplified interface

$deviceHtmlPath = "Inc\eth\fs\device.html"

if (Test-Path $deviceHtmlPath) {
    Write-Host "Converting device.html to hex format..."
    
    # Read file as bytes
    $bytes = [System.IO.File]::ReadAllBytes($deviceHtmlPath)
    
    # Convert to hex format like fsdata.c expects
    $hexOutput = @()
    
    # Add filename path first (with leading slash and null terminator)
    $filename = "/device.html"
    $filenameBytes = [System.Text.Encoding]::UTF8.GetBytes($filename) + @(0)
    
    Write-Host "Filename hex:"
    $filenameHex = ($filenameBytes | ForEach-Object { "0x{0:x2}" -f $_ }) -join ", "
    Write-Host $filenameHex
    
    Write-Host "`nContent hex (first 200 bytes):"
    # Show first 200 bytes of content
    $contentHex = ($bytes[0..199] | ForEach-Object { "0x{0:x2}" -f $_ }) -join ", "
    Write-Host $contentHex
    
    Write-Host "`nTotal file size: $($bytes.Length) bytes"
    Write-Host "Filename path length: $($filenameBytes.Length) bytes"
    
    # Save full hex to a file for reference
    $fullHex = ($bytes | ForEach-Object { "0x{0:x2}" -f $_ }) -join ", "
    $fullHex | Out-File "device_html_hex.txt"
    Write-Host "Full hex saved to device_html_hex.txt"
    
} else {
    Write-Host "Error: device.html not found at $deviceHtmlPath"
}
