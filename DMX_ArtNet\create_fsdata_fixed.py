#!/usr/bin/env python3
"""
Generate fsdata.c without HTTP headers to prevent text display issues at top of pages.
This is the solution that worked before to avoid the text display problem.
"""

import os
import sys

def file_to_hex_array(filepath, varname):
    """Convert file to C hex array without HTTP headers"""
    if not os.path.exists(filepath):
        print(f"Warning: {filepath} not found, skipping...")
        return None, 0
    
    print(f"Processing {filepath} (no HTTP headers)...")
    
    with open(filepath, 'rb') as f:
        content = f.read()
    
    # Create filename path with leading slash
    filename_path = f"/{filepath}".encode('utf-8') + b'\x00'
    
    # Start array declaration
    lines = [f"static const unsigned char data_{varname}[] = {{"]
    lines.append(f"\t/* /{filepath} */")
    
    # Add filename path as hex
    hex_filename = ', '.join(f'0x{b:02x}' for b in filename_path)
    lines.append(f"\t{hex_filename},")
    
    # Add file content directly as hex (NO HTTP headers)
    hex_content = ', '.join(f'0x{b:02x}' for b in content)
    
    # Split into lines of reasonable length
    hex_parts = hex_content.split(', ')
    for i in range(0, len(hex_parts), 16):
        line_parts = hex_parts[i:i+16]
        lines.append(f"\t{', '.join(line_parts)},")
    
    lines.append("};")
    lines.append("")
    
    return '\n'.join(lines), len(filename_path)

def main():
    # Change to fs directory
    os.chdir('Inc/eth/fs')
    
    # Backup current fsdata.c
    if os.path.exists('fsdata.c'):
        os.rename('fsdata.c', 'fsdata.c.backup')
    
    # File list
    files = [
        ("device.html", "device_html"),
        ("dfu.html", "dfu_html"),
        ("index.html", "index_html"),
        ("style.css", "style_css"),
        ("min/device.html", "min_device_html"),
        ("min/dfu.html", "min_dfu_html"),
        ("min/index.html", "min_index_html"),
        ("min/style.css", "min_style_css")
    ]
    
    # Start fsdata.c
    fsdata_content = [
        '#include "lwip/apps/fs.h"',
        '#include "lwip/def.h"',
        '',
        '#define file_NULL (struct fsdata_file *) NULL',
        '',
        '#ifndef FS_FILE_FLAGS_HEADER_INCLUDED',
        '#define FS_FILE_FLAGS_HEADER_INCLUDED 1',
        '#endif',
        '#ifndef FS_FILE_FLAGS_HEADER_PERSISTENT',
        '#define FS_FILE_FLAGS_HEADER_PERSISTENT 0',
        '#endif',
        ''
    ]
    
    # Process files and collect info
    processed_files = []
    
    for filepath, varname in files:
        array_code, filename_len = file_to_hex_array(filepath, varname)
        if array_code:
            fsdata_content.append(array_code)
            processed_files.append((varname, filename_len))
    
    # Generate file structures (reverse order for proper linking)
    prev_file = "file_NULL"
    for varname, filename_len in reversed(processed_files):
        fsdata_content.extend([
            f"const struct fsdata_file file_{varname}[] = {{ {{",
            f"{prev_file},",
            f"data_{varname},",
            f"data_{varname} + {filename_len},",
            f"sizeof(data_{varname}) - {filename_len},",
            "0",
            "}};",
            ""
        ])
        prev_file = f"file_{varname}"
    
    # Add final definitions
    fsdata_content.extend([
        f"#define FS_ROOT {prev_file}",
        f"#define FS_NUMFILES {len(processed_files)}",
        ""
    ])
    
    # Write fsdata.c
    with open('fsdata.c', 'w') as f:
        f.write('\n'.join(fsdata_content))
    
    print(f"\nfsdata.c generated successfully!")
    print(f"Files processed: {len(processed_files)}")
    print("This version should NOT display text at the top of web pages.")
    print("\nKey features:")
    print("- No HTTP headers in file data")
    print("- Direct file content embedding")
    print("- Proper lwIP structure")

if __name__ == "__main__":
    main()
