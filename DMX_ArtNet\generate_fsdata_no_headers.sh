#!/bin/bash

# Script to generate fsdata.c with makefsdata-like options but without HTTP headers
# This prevents text from appearing at the top of web pages

echo "Generating fsdata.c without HTTP headers to prevent display issues..."

cd Inc/eth/fs

# Backup current fsdata.c
cp fsdata.c fsdata.c.backup

# Create new fsdata.c with proper structure
cat > fsdata.c << 'EOF'
#include "lwip/apps/fs.h"
#include "lwip/def.h"

#define file_NULL (struct fsdata_file *) NULL

#ifndef FS_FILE_FLAGS_HEADER_INCLUDED
#define FS_FILE_FLAGS_HEADER_INCLUDED 1
#endif
#ifndef FS_FILE_FLAGS_HEADER_PERSISTENT
#define FS_FILE_FLAGS_HEADER_PERSISTENT 0
#endif

EOF

# Function to convert file to C array without HTTP headers
convert_file_no_headers() {
    local filepath="$1"
    local varname="$2"
    
    if [ ! -f "$filepath" ]; then
        echo "Warning: $filepath not found, skipping..."
        return 1
    fi
    
    echo "Processing $filepath (no HTTP headers)..."
    
    # Add array declaration
    echo "static const unsigned char data_${varname}[] = {" >> fsdata.c
    echo "	/* /$filepath */" >> fsdata.c
    
    # Add filename as hex bytes (path offset)
    printf "\t" >> fsdata.c
    echo -n "/$filepath" | xxd -p | sed 's/../0x&, /g' >> fsdata.c
    printf "0x00,\n" >> fsdata.c
    
    # Add file content directly as hex bytes (NO HTTP headers)
    printf "\t" >> fsdata.c
    xxd -p "$filepath" | sed 's/../0x&, /g' | tr -d '\n' >> fsdata.c
    printf "\n" >> fsdata.c
    
    echo "};" >> fsdata.c
    echo "" >> fsdata.c
    
    # Return filename length + 1 for null terminator + 1 for leading slash
    echo $((${#filepath} + 2))
}

# Process files and store their info
declare -a processed_files=()
declare -a processed_vars=()
declare -a filename_lengths=()

# File list with proper variable names
files=(
    "device.html:device_html"
    "dfu.html:dfu_html" 
    "index.html:index_html"
    "style.css:style_css"
    "min/device.html:min_device_html"
    "min/dfu.html:min_dfu_html"
    "min/index.html:min_index_html"
    "min/style.css:min_style_css"
)

for file_entry in "${files[@]}"; do
    filepath="${file_entry%:*}"
    varname="${file_entry#*:}"
    
    length=$(convert_file_no_headers "$filepath" "$varname")
    if [ $? -eq 0 ]; then
        processed_files+=("$filepath")
        processed_vars+=("$varname")
        filename_lengths+=("$length")
    fi
done

# Generate file structures (linked list) - reverse order for proper linking
prev_file="file_NULL"
for ((i=${#processed_vars[@]}-1; i>=0; i--)); do
    varname="${processed_vars[$i]}"
    length="${filename_lengths[$i]}"
    
    cat >> fsdata.c << EOF
const struct fsdata_file file_${varname}[] = { {
$prev_file,
data_${varname},
data_${varname} + ${length},
sizeof(data_${varname}) - ${length},
0
}};

EOF
    prev_file="file_${varname}"
done

# Add final definitions
echo "#define FS_ROOT $prev_file" >> fsdata.c
echo "#define FS_NUMFILES ${#processed_vars[@]}" >> fsdata.c
echo "" >> fsdata.c

echo "fsdata.c generated successfully without HTTP headers!"
echo "Files processed: ${#processed_vars[@]}"
echo "This version should NOT display text at the top of web pages."
echo ""
echo "Key differences from standard makefsdata:"
echo "- No HTTP headers in file data"
echo "- Flags set to 0 to prevent header processing"
echo "- Direct file content embedding"
