# PowerShell script to regenerate fsdata.c with current HTML modifications
# This includes the simplified interface and correct flags to prevent text at top of pages

Write-Host "Regenerating fsdata.c with current HTML modifications..."

# Change to fs directory
Set-Location "Inc\eth\fs"

# Backup current fsdata.c
if (Test-Path "fsdata.c") {
    Copy-Item "fsdata.c" "fsdata.c.backup_before_regen"
    Write-Host "Backup created: fsdata.c.backup_before_regen"
}

# Function to convert file to hex array
function ConvertTo-HexArray {
    param(
        [string]$FilePath,
        [string]$VarName
    )
    
    if (-not (Test-Path $FilePath)) {
        Write-Host "Warning: $FilePath not found, skipping..."
        return $null
    }
    
    Write-Host "Processing $FilePath..."
    
    # Read file as bytes
    $bytes = [System.IO.File]::ReadAllBytes($FilePath)
    
    # Create filename path with leading slash and null terminator
    $filename = "/$FilePath"
    $filenameBytes = [System.Text.Encoding]::UTF8.GetBytes($filename) + @(0)
    
    # Convert to hex format
    $filenameHex = ($filenameBytes | ForEach-Object { "0x{0:x2}" -f $_ }) -join ", "
    $contentHex = ($bytes | ForEach-Object { "0x{0:x2}" -f $_ }) -join ", "
    
    # Format for C array
    $result = @"
static const unsigned char data_$VarName[] = {
	/* /$FilePath */
	$filenameHex,
	$contentHex
};

"@
    
    return @{
        Content = $result
        FilenameLength = $filenameBytes.Length
        VarName = $VarName
    }
}

# File definitions
$files = @(
    @{ Path = "device.html"; VarName = "device_html" },
    @{ Path = "dfu.html"; VarName = "dfu_html" },
    @{ Path = "index.html"; VarName = "index_html" },
    @{ Path = "style.css"; VarName = "style_css" },
    @{ Path = "min\device.html"; VarName = "min_device_html" },
    @{ Path = "min\dfu.html"; VarName = "min_dfu_html" },
    @{ Path = "min\index.html"; VarName = "min_index_html" },
    @{ Path = "min\style.css"; VarName = "min_style_css" }
)

# Start building fsdata.c
$fsdataContent = @"
#include "lwip/apps/fs.h"
#include "lwip/def.h"

#define file_NULL (struct fsdata_file *) NULL

/* Flags set to prevent text display at top of pages - this was the solution that worked */
#ifndef FS_FILE_FLAGS_HEADER_INCLUDED
#define FS_FILE_FLAGS_HEADER_INCLUDED 0
#endif
#ifndef FS_FILE_FLAGS_HEADER_PERSISTENT
#define FS_FILE_FLAGS_HEADER_PERSISTENT 0
#endif

"@

# Process files and collect info
$processedFiles = @()

foreach ($file in $files) {
    $result = ConvertTo-HexArray -FilePath $file.Path -VarName $file.VarName
    if ($result) {
        $fsdataContent += $result.Content
        $processedFiles += @{
            VarName = $result.VarName
            FilenameLength = $result.FilenameLength
        }
    }
}

# Generate file structures (reverse order for proper linking)
$prevFile = "file_NULL"
for ($i = $processedFiles.Count - 1; $i -ge 0; $i--) {
    $file = $processedFiles[$i]
    $fsdataContent += @"
const struct fsdata_file file_$($file.VarName)[] = { {
$prevFile,
data_$($file.VarName),
data_$($file.VarName) + $($file.FilenameLength),
sizeof(data_$($file.VarName)) - $($file.FilenameLength),
0
}};

"@
    $prevFile = "file_$($file.VarName)"
}

# Add final definitions
$fsdataContent += @"
#define FS_ROOT $prevFile
#define FS_NUMFILES $($processedFiles.Count)

"@

# Write the file
$fsdataContent | Out-File -FilePath "fsdata.c" -Encoding ASCII

Write-Host ""
Write-Host "✅ fsdata.c regenerated successfully!"
Write-Host "✅ Files processed: $($processedFiles.Count)"
Write-Host "✅ Interface simplified: Gateway and iPad IP hidden"
Write-Host "✅ Flags set to prevent text at top of pages"
Write-Host "✅ Both normal and minified versions updated"
