#!/usr/bin/env python3
"""
Update fsdata.c with current HTML files while preserving the correct flags
to prevent text display issues at top of pages.
"""

import os
import sys

def read_file_as_hex(filepath):
    """Read file and return as hex array"""
    if not os.path.exists(filepath):
        print(f"Warning: {filepath} not found")
        return None
    
    with open(filepath, 'rb') as f:
        content = f.read()
    
    # Create filename path with leading slash and null terminator
    filename_path = f"/{filepath}".encode('utf-8') + b'\x00'
    
    # Convert to hex arrays
    hex_filename = [f'0x{b:02x}' for b in filename_path]
    hex_content = [f'0x{b:02x}' for b in content]
    
    return hex_filename, hex_content, len(filename_path)

def format_hex_array(hex_data, indent="\t"):
    """Format hex array with proper line breaks"""
    lines = []
    for i in range(0, len(hex_data), 16):
        line_data = hex_data[i:i+16]
        lines.append(f"{indent}{','.join(line_data)},")
    return lines

def main():
    os.chdir('Inc/eth/fs')
    
    # Backup current fsdata.c
    if os.path.exists('fsdata.c'):
        os.rename('fsdata.c', 'fsdata.c.backup_before_update')
    
    print("Updating fsdata.c with current HTML modifications...")
    
    # File definitions
    files = [
        ("device.html", "device_html"),
        ("dfu.html", "dfu_html"),
        ("index.html", "index_html"),
        ("style.css", "style_css"),
        ("min/device.html", "min_device_html"),
        ("min/dfu.html", "min_dfu_html"),
        ("min/index.html", "min_index_html"),
        ("min/style.css", "min_style_css")
    ]
    
    # Start building fsdata.c content
    fsdata_lines = [
        '#include "lwip/apps/fs.h"',
        '#include "lwip/def.h"',
        '',
        '#define file_NULL (struct fsdata_file *) NULL',
        '',
        '/* Flags set to prevent text display at top of pages */',
        '#ifndef FS_FILE_FLAGS_HEADER_INCLUDED',
        '#define FS_FILE_FLAGS_HEADER_INCLUDED 0',
        '#endif',
        '#ifndef FS_FILE_FLAGS_HEADER_PERSISTENT',
        '#define FS_FILE_FLAGS_HEADER_PERSISTENT 0',
        '#endif',
        ''
    ]
    
    # Process each file
    processed_files = []
    
    for filepath, varname in files:
        result = read_file_as_hex(filepath)
        if result:
            hex_filename, hex_content, filename_len = result
            print(f"Processing {filepath}...")
            
            # Add array declaration
            fsdata_lines.append(f'static const unsigned char data_{varname}[] = {{')
            fsdata_lines.append(f'\t/* /{filepath} */')
            
            # Add filename
            fsdata_lines.extend(format_hex_array(hex_filename))
            
            # Add content
            fsdata_lines.extend(format_hex_array(hex_content))
            
            fsdata_lines.append('};')
            fsdata_lines.append('')
            
            processed_files.append((varname, filename_len))
    
    # Generate file structures (reverse order for proper linking)
    prev_file = "file_NULL"
    for varname, filename_len in reversed(processed_files):
        fsdata_lines.extend([
            f'const struct fsdata_file file_{varname}[] = {{ {{',
            f'{prev_file},',
            f'data_{varname},',
            f'data_{varname} + {filename_len},',
            f'sizeof(data_{varname}) - {filename_len},',
            '0',
            '}};',
            ''
        ])
        prev_file = f'file_{varname}'
    
    # Add final definitions
    fsdata_lines.extend([
        f'#define FS_ROOT {prev_file}',
        f'#define FS_NUMFILES {len(processed_files)}',
        ''
    ])
    
    # Write the file
    with open('fsdata.c', 'w') as f:
        f.write('\n'.join(fsdata_lines))
    
    print(f"\nfsdata.c updated successfully!")
    print(f"Files processed: {len(processed_files)}")
    print("✅ Interface simplified: Gateway and iPad IP removed from display")
    print("✅ Backend fields preserved but hidden")
    print("✅ Flags set to prevent text at top of pages")

if __name__ == "__main__":
    main()
